<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getPracticeDetail } from '@/api/exam'
import type { PracticeResult, ExamQuestion } from '@/types/api/Exam'
import { navigateTo } from '@uni-helper/uni-promises'

const practiceDetail = ref<PracticeResult | null>(null)
const loading = ref(false)
const practiceId = ref<number>()

// 页面加载时获取参数
onLoad((options: any) => {
  if (options.practiceId) {
    practiceId.value = Number(options.practiceId)
    fetchPracticeDetail()
  }
})

// 获取练习详情
const fetchPracticeDetail = async () => {
  if (!practiceId.value) return

  try {
    loading.value = true
    const result = await getPracticeDetail({ practiceId: practiceId.value })
    practiceDetail.value = result
  } catch (error) {
    console.error('获取练习详情失败:', error)
    uni.showToast({
      title: '获取练习详情失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'SINGLE_CHOICE':
      return '单选题'
    case 'MULTIPLE_CHOICE':
      return '多选题'
    case 'TRUE_FALSE':
      return '判断题'
    default:
      return type
  }
}

// 查看题目详情
const viewQuestion = (question: ExamQuestion, index: number) => {
  // 这里可以跳转到题目详情页面或者显示题目内容
  uni.showModal({
    title: `第${index + 1}题 - ${getQuestionTypeText(question.type)}`,
    content: question.title,
    showCancel: false,
    confirmText: '确定'
  })
}

// 继续练习
const continuePractice = () => {
  if (practiceId.value) {
    navigateTo({
      url: `/pages/training-center/practice/practice?practiceId=${practiceId.value}`
    })
  }
}


</script>

<template>
  <view class="practice-detail-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习详情 -->
    <view v-else-if="practiceDetail" class="detail-content">
      <!-- 基本信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">练习信息</text>
        </view>
        <view class="card-body">
          <view class="info-row">
            <text class="label">题库名称</text>
            <text class="value">{{ practiceDetail.bankNames?.join('、') || '--' }}</text>
          </view>
          <view class="info-row">
            <text class="label">练习ID</text>
            <text class="value">{{ practiceDetail.practiceId }}</text>
          </view>
          <view class="info-row">
            <text class="label">开始时间</text>
            <text class="value">{{ formatTime(practiceDetail.startTime) }}</text>
          </view>
          <view class="info-row">
            <text class="label">总题数</text>
            <text class="value">{{ practiceDetail.totalCount }}题</text>
          </view>
          <view class="info-row">
            <text class="label">单选题</text>
            <text class="value">{{ practiceDetail.singleChoiceCount }}题</text>
          </view>
          <view class="info-row">
            <text class="label">多选题</text>
            <text class="value">{{ practiceDetail.multipleChoiceCount }}题</text>
          </view>
        </view>
      </view>

      <!-- 题目列表 -->
      <view class="questions-card">
        <view class="card-header">
          <text class="card-title">题目列表</text>
          <text class="question-count">共{{ practiceDetail.questions?.length || 0 }}题</text>
        </view>
        <view class="questions-list">
          <view
            v-for="(question, index) in practiceDetail.questions"
            :key="question.id"
            class="question-item"
            @click="viewQuestion(question, index)"
          >
            <view class="question-header">
              <view class="question-number">{{ index + 1 }}</view>
              <view class="question-type">{{ getQuestionTypeText(question.type) }}</view>
              <view class="question-score" v-if="question.score">{{ question.score }}分</view>
            </view>
            <view class="question-title">{{ question.title }}</view>
            <view class="question-options" v-if="question.options?.length">
              <text class="options-count">{{ question.options.length }}个选项</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="continue-btn" @click="continuePractice">
          继续练习
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-container">
      <text class="empty-text">练习详情不存在</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "练习详情"
  }
}
</route>

<style scoped lang="scss">
.practice-detail-page {
  background-color: #f7f7f5;
  min-height: 100vh;
  padding: 16px;
  padding-bottom: 80px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card, .questions-card {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.question-count {
  font-size: 14px;
  color: #666;
}

.card-body {
  padding: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 14px;
  color: #666;
}

.value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.questions-list {
  max-height: 400px;
  overflow-y: auto;
}

.question-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.question-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.question-number {
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 20px;
  text-align: center;
}

.question-type {
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.question-score {
  background-color: #fff7e6;
  color: #fa8c16;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.question-title {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
}

.question-options {
  display: flex;
  align-items: center;
}

.options-count {
  font-size: 12px;
  color: #999;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

.continue-btn {
  width: 100%;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;

  &:active {
    background-color: #096dd9;
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.empty-text {
  font-size: 16px;
  color: #666;
}
</style>
